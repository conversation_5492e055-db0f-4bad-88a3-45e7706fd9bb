<template>
  <!-- Step 3 Content -->
  <div class="flex flex-col items-center justify-center h-full min-h-[400px]">
          <div class="text-center space-y-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            
            <h2 class="text-3xl font-bold text-gray-800">Step 3 - Thanh toán</h2>
            
            <p class="text-lg text-gray-600 max-w-md">
              Trang này sẽ hiển thị các ph<PERSON><PERSON><PERSON> thức thanh toán và xử lý giao dịch mua bảo hiểm.
            </p>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-lg">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-yellow-800 font-medium">Đang phát triển</span>
              </div>
              <p class="text-yellow-700 mt-1 text-sm">
                Nội dung chi tiết của bước này sẽ được bổ sung sau.
              </p>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-center gap-4 pt-8">
              <button
                @click="goBack"
                class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Quay lại
              </button>
              <button
                @click="completePayment"
                class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Hoàn tất thanh toán
              </button>
            </div>
          </div>
        </div>
</template>

<script setup lang="ts">
// SEO Meta
useHead({
  title: 'Bước 3: Thanh toán - BIC',
  meta: [
    {
      name: 'description',
      content: 'Thanh toán bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô'
    }
  ]
})

// Router
const router = useRouter()

// Methods
const goBack = () => {
  router.push('/insurance/purchase/step-2')
}

const completePayment = () => {
  // Handle payment completion
  alert('Thanh toán thành công! (Demo)')
  router.push('/')
}
</script>

<style scoped>
/* Custom styles for step 3 */
</style>
