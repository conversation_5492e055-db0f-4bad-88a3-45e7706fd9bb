<template>
  <!-- Form Content -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Thông tin chủ xe -->
          <div class="space-y-4">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Thông tin chủ xe</h3>

            <!-- Tên công ty -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Tên công ty <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.companyName"
                type="text"
                maxlength="200"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                :class="{ 'border-red-500': errors.companyName }"
                placeholder="Nhập tên công ty"
                @blur="validateField('companyName')"
              />
              <span v-if="errors.companyName" class="text-red-500 text-sm">{{ errors.companyName }}</span>
            </div>

            <!-- <PERSON><PERSON> số thuế -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Mã số thuế <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.taxCode"
                type="text"
                maxlength="200"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                :class="{ 'border-red-500': errors.taxCode }"
                placeholder="Nhập mã số thuế"
                @blur="validateField('taxCode')"
              />
              <span v-if="errors.taxCode" class="text-red-500 text-sm">{{ errors.taxCode }}</span>
            </div>

            <!-- Địa chỉ công ty -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Địa chỉ công ty <span class="text-red-500">*</span>
              </label>
              <textarea
                v-model="formData.companyAddress"
                rows="3"
                class="w-full px-[14px] py-3 border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none resize-none"
                :class="{ 'border-red-500': errors.companyAddress }"
                placeholder="Nhập địa chỉ công ty đầy đủ"
                @blur="validateField('companyAddress')"
              ></textarea>
              <span v-if="errors.companyAddress" class="text-red-500 text-sm">{{ errors.companyAddress }}</span>
            </div>

            <!-- Số điện thoại -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Số điện thoại <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.phoneNumber"
                type="tel"
                maxlength="10"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                :class="{ 'border-red-500': errors.phoneNumber }"
                placeholder="Nhập số điện thoại"
                @input="onPhoneInput"
                @blur="validateField('phoneNumber')"
              />
              <span v-if="errors.phoneNumber" class="text-red-500 text-sm">{{ errors.phoneNumber }}</span>
            </div>

            <!-- Email nhận GCN bảo hiểm -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Email nhận GCN bảo hiểm <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.email"
                type="email"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                :class="{ 'border-red-500': errors.email }"
                placeholder="Nhập địa chỉ email"
                @blur="validateField('email')"
              />
              <span v-if="errors.email" class="text-red-500 text-sm">{{ errors.email }}</span>
            </div>

            <!-- Lưu thông tin chủ xe -->
            <div class="flex items-center space-x-2">
              <input
                v-model="formData.saveOwnerInfo"
                type="checkbox"
                id="saveOwnerInfo"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <label for="saveOwnerInfo" class="text-sm text-gray-700">
                Lưu thông tin chủ xe cho lần sau
              </label>
            </div>
          </div>

          <!-- Thông tin xe -->
          <div class="space-y-4">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Thông tin xe</h3>

            <!-- Biển kiểm soát -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Biển kiểm soát <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.licensePlate"
                type="text"
                maxlength="200"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                :class="{ 'border-red-500': errors.licensePlate }"
                placeholder="Nhập biển kiểm soát"
                @blur="validateField('licensePlate')"
              />
              <span v-if="errors.licensePlate" class="text-red-500 text-sm">{{ errors.licensePlate }}</span>
            </div>

            <!-- Số chỗ -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Số chỗ <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.seatCount"
                type="number"
                min="1"
                max="9"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                :class="{ 'border-red-500': errors.seatCount }"
                placeholder="Nhập số chỗ"
                @blur="validateField('seatCount')"
              />
              <span v-if="errors.seatCount" class="text-red-500 text-sm">{{ errors.seatCount }}</span>
            </div>

            <!-- Số khung -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Số khung
              </label>
              <input
                v-model="formData.chassisNumber"
                type="text"
                maxlength="200"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                placeholder="Nhập số khung (không bắt buộc)"
              />
            </div>

            <!-- Số máy -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Số máy
              </label>
              <input
                v-model="formData.engineNumber"
                type="text"
                maxlength="200"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                placeholder="Nhập số máy (không bắt buộc)"
              />
            </div>

            <!-- Trọng tải (Disabled) -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-[#919eab]">
                Trọng tải
              </label>
              <input
                type="text"
                value="Trên 15 tấn"
                disabled
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] bg-gray-100 text-[#919eab] cursor-not-allowed"
              />
            </div>

            <!-- Loại xe (Disabled) -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-[#919eab]">
                Loại xe
              </label>
              <input
                type="text"
                value="Xe ô tô chở hàng (Xe tải)"
                disabled
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] bg-gray-100 text-[#919eab] cursor-not-allowed"
              />
            </div>

            <!-- Mục đích sử dụng (Disabled) -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-[#919eab]">
                Mục đích sử dụng
              </label>
              <input
                type="text"
                value="Kinh doanh vận tải"
                disabled
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] bg-gray-100 text-[#919eab] cursor-not-allowed"
              />
            </div>

            <!-- Ngày bắt đầu -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Ngày bắt đầu <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.startDate"
                type="datetime-local"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                :class="{ 'border-red-500': errors.startDate }"
                @blur="validateField('startDate')"
                @change="updateEndDate"
              />
              <span v-if="errors.startDate" class="text-red-500 text-sm">{{ errors.startDate }}</span>
            </div>

            <!-- Ngày kết thúc (Disabled) -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-[#919eab]">
                Ngày kết thúc
              </label>
              <input
                v-model="formData.endDate"
                type="datetime-local"
                disabled
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] bg-gray-100 text-[#919eab] cursor-not-allowed"
              />
            </div>
          </div>

          <!-- Buttons -->
          <div class="flex justify-end gap-4 pt-8">
            <button
              type="button"
              @click="goBack"
              class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Quay lại
            </button>
            <button
              type="submit"
              class="px-6 py-3 bg-[#3079ff] text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Tiếp tục
            </button>
          </div>
        </form>
</template>

<script setup lang="ts">
// SEO Meta
useHead({
  title: 'Bước 1: Khai báo thông tin mua bảo hiểm - BIC',
  meta: [
    {
      name: 'description',
      content: 'Khai báo thông tin chủ xe và thông tin xe để mua bảo hiểm bắt buộc trách nhiệm dân sự'
    }
  ]
})

// Router
const router = useRouter()

// Form data according to URD SCR-1
const formData = ref({
  // Thông tin chủ xe
  companyName: '',
  taxCode: '',
  companyAddress: '',
  phoneNumber: '',
  email: '',
  saveOwnerInfo: false,

  // Thông tin xe
  licensePlate: '',
  seatCount: null as number | null,
  chassisNumber: '',
  engineNumber: '',
  startDate: '',
  endDate: ''
})

// Error tracking
const errors = ref({
  companyName: '',
  taxCode: '',
  companyAddress: '',
  phoneNumber: '',
  email: '',
  licensePlate: '',
  seatCount: '',
  startDate: ''
})

// Initialize default values
onMounted(() => {
  // Set default start date to current time
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')

  formData.value.startDate = `${year}-${month}-${day}T${hours}:${minutes}`
  updateEndDate()
})

// Validation functions
const validateField = (fieldName: string) => {
  errors.value[fieldName as keyof typeof errors.value] = ''

  switch (fieldName) {
    case 'companyName':
      if (!formData.value.companyName.trim()) {
        errors.value.companyName = 'Tên công ty không được để trống'
      } else if (formData.value.companyName.length > 200) {
        errors.value.companyName = 'Tên công ty không được vượt quá 200 ký tự'
      }
      break

    case 'taxCode':
      if (!formData.value.taxCode.trim()) {
        errors.value.taxCode = 'Mã số thuế không được để trống'
      } else if (formData.value.taxCode.length > 200) {
        errors.value.taxCode = 'Mã số thuế không được vượt quá 200 ký tự'
      }
      break

    case 'companyAddress':
      if (!formData.value.companyAddress.trim()) {
        errors.value.companyAddress = 'Địa chỉ công ty không được để trống'
      }
      break

    case 'phoneNumber':
      if (!formData.value.phoneNumber.trim()) {
        errors.value.phoneNumber = 'Số điện thoại không được để trống'
      } else if (!/^\d+$/.test(formData.value.phoneNumber)) {
        errors.value.phoneNumber = 'Số điện thoại chỉ được chứa số'
      } else if (formData.value.phoneNumber.length > 10) {
        errors.value.phoneNumber = 'Số điện thoại không được vượt quá 10 ký tự'
      }
      break

    case 'email':
      if (!formData.value.email.trim()) {
        errors.value.email = 'Email không được để trống'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.email)) {
        errors.value.email = 'Email không đúng định dạng'
      }
      break

    case 'licensePlate':
      if (!formData.value.licensePlate.trim()) {
        errors.value.licensePlate = 'Biển kiểm soát không được để trống'
      } else if (formData.value.licensePlate.length > 200) {
        errors.value.licensePlate = 'Biển kiểm soát không được vượt quá 200 ký tự'
      }
      break

    case 'seatCount':
      if (!formData.value.seatCount) {
        errors.value.seatCount = 'Số chỗ không được để trống'
      } else if (formData.value.seatCount <= 0 || formData.value.seatCount > 9) {
        errors.value.seatCount = 'Số chỗ phải từ 1 đến 9'
      }
      break

    case 'startDate':
      if (!formData.value.startDate) {
        errors.value.startDate = 'Ngày bắt đầu không được để trống'
      }
      break
  }
}

// Phone input handler - only allow numbers
const onPhoneInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/\D/g, '') // Remove non-digits
  formData.value.phoneNumber = value
}

// Update end date when start date changes
const updateEndDate = () => {
  if (formData.value.startDate) {
    const startDate = new Date(formData.value.startDate)
    const endDate = new Date(startDate)
    endDate.setDate(endDate.getDate() + 30) // Add 30 days

    const year = endDate.getFullYear()
    const month = String(endDate.getMonth() + 1).padStart(2, '0')
    const day = String(endDate.getDate()).padStart(2, '0')
    const hours = String(endDate.getHours()).padStart(2, '0')
    const minutes = String(endDate.getMinutes()).padStart(2, '0')

    formData.value.endDate = `${year}-${month}-${day}T${hours}:${minutes}`
  }
}

// Form validation
const validateForm = () => {
  const requiredFields = ['companyName', 'taxCode', 'companyAddress', 'phoneNumber', 'email', 'licensePlate', 'seatCount', 'startDate']
  let isValid = true

  requiredFields.forEach(field => {
    validateField(field)
    if (errors.value[field as keyof typeof errors.value]) {
      isValid = false
    }
  })

  return isValid
}

// Methods
const handleSubmit = () => {
  if (validateForm()) {
    console.log('Form submitted:', formData.value)
    // Navigate to step 2
    router.push('/insurance/purchase/step-2')
  } else {
    console.log('Form has validation errors')
  }
}

const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* Helvetica Neue font family */
.font-helvetica-neue-regular {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 400;
}

.font-helvetica-neue-bold {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 700;
}

/* Custom styles for form inputs according to Figma design variables */
input:focus, select:focus, textarea:focus {
  box-shadow: 0 0 0 2px rgba(48, 121, 255, 0.2);
}

/* Placeholder styling using design variable */
input::placeholder, textarea::placeholder {
  color: #919eab; /* text/disabled */
}

/* Input field styling according to Figma variables */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="number"],
input[type="datetime-local"],
textarea {
  height: 54px; /* textfield/[outlined][medium]--height */
  padding-left: 14px; /* textfield/[outlined]--px */
  padding-right: 14px; /* textfield/[outlined]--px */
  border-radius: 8px; /* textfield/[outlined]--radius */
}

textarea {
  height: auto;
  padding-top: 14px;
  padding-bottom: 14px;
}

/* Disabled input styling */
input:disabled {
  background-color: #f9fafb;
  color: #919eab; /* text/disabled */
  cursor: not-allowed;
}

/* Error state styling */
.border-red-500 {
  border-color: #ef4444 !important;
}

/* Checkbox styling */
input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

/* Button styling */
button {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
}

/* Form section headers */
h3 {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 600;
}

/* Label styling */
label {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 500;
}

/* Error message styling */
.text-red-500 {
  color: #ef4444;
  font-size: 14px;
}
</style>
