<template>
  <div class="bg-[#ffffff] relative min-h-screen">
    <!-- Title Section -->
    <div class="flex flex-col items-center gap-[9px] pt-[115px] mb-[40px]">
      <div class="flex flex-col items-center gap-10">
        <div class="font-bold text-[40px] leading-[1.2] text-center text-neutral-700 max-w-4xl">
          <p><PERSON><PERSON>o hiể<PERSON> bắt buộc trách nhiệm dân sự của chủ xe ô tô</p>
        </div>
      </div>
      <div class="font-normal text-[16px] leading-[1.2] text-center text-neutral-700">
        <p>Vui lòng cung cấp đầy đủ các thông tin dưới đây để tiếp tục.</p>
      </div>
    </div>

    <!-- Stepper Navigation -->
    <div class="flex items-center justify-center mb-[40px]">
      <div class="flex items-center gap-0 relative">
        <!-- Step 1 -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="bg-[#3079ff] flex items-center justify-center w-[60px] h-[60px] rounded-[30px] px-4 py-3">
            <span class="font-bold text-[28px] leading-[1.2] text-center text-[#ffffff]">1</span>
          </div>
          <div class="text-center w-full">
            <p class="text-[16px] leading-[1.2] font-bold text-[#3079ff]">
              Khai báo thông tin mua bảo hiểm
            </p>
          </div>
        </div>

        <!-- Line 1 -->
        <div class="w-[257px] h-0 relative">
          <div class="absolute top-[-2px] left-0 right-0 bottom-0">
            <svg width="257" height="4" viewBox="0 0 257 4" fill="none">
              <path d="M0 2H257" stroke="#E0E0E0" stroke-width="2"/>
            </svg>
          </div>
        </div>

        <!-- Step 2 -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="border border-[#3079ff] border-solid flex items-center justify-center w-[60px] h-[60px] rounded-[30px] px-4 py-3">
            <span class="font-bold text-[28px] leading-[1.2] text-center text-[#3079ff]">2</span>
          </div>
          <div class="text-center w-full">
            <p class="text-[16px] leading-[1.2] font-normal text-[#000000]">Xác nhận thông tin</p>
          </div>
        </div>

        <!-- Line 2 -->
        <div class="w-[253px] h-0 relative">
          <div class="absolute top-[-2px] left-0 right-0 bottom-0">
            <svg width="253" height="4" viewBox="0 0 253 4" fill="none">
              <path d="M0 2H253" stroke="#E0E0E0" stroke-width="2"/>
            </svg>
          </div>
        </div>

        <!-- Step 3 -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="border border-[#3079ff] border-solid flex items-center justify-center w-[60px] h-[60px] rounded-[30px] px-4 py-3">
            <span class="font-bold text-[28px] leading-[1.2] text-center text-[#3079ff]">3</span>
          </div>
          <div class="text-center w-full">
            <p class="text-[16px] leading-[1.2] font-normal text-[#000000]">Thanh toán</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Content Area -->
    <div class="flex justify-center pb-[200px]">
      <div class="bg-[#ffffff] border border-[#e9ecee] border-solid rounded-lg w-[1140px] min-h-[1478px] p-8">
        <!-- Form Content -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Thông tin chủ xe -->
          <div class="space-y-4">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Thông tin chủ xe</h3>
            
            <!-- Họ và tên -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Họ và tên <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.ownerName"
                type="text"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                placeholder="Nhập họ và tên chủ xe"
                required
              />
            </div>

            <!-- Số điện thoại -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Số điện thoại <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.phoneNumber"
                type="tel"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                placeholder="Nhập số điện thoại"
                required
              />
            </div>

            <!-- Email -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Email <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.email"
                type="email"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                placeholder="Nhập địa chỉ email"
                required
              />
            </div>

            <!-- Địa chỉ -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Địa chỉ <span class="text-red-500">*</span>
              </label>
              <textarea
                v-model="formData.address"
                rows="3"
                class="w-full px-[14px] py-3 border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none resize-none"
                placeholder="Nhập địa chỉ đầy đủ"
                required
              ></textarea>
            </div>
          </div>

          <!-- Thông tin xe -->
          <div class="space-y-4">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Thông tin xe</h3>
            
            <!-- Biển số xe -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Biển số xe <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.licensePlate"
                type="text"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                placeholder="Nhập biển số xe"
                required
              />
            </div>

            <!-- Loại xe -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Loại xe <span class="text-red-500">*</span>
              </label>
              <select
                v-model="formData.vehicleType"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                required
              >
                <option value="">Chọn loại xe</option>
                <option value="car">Ô tô con</option>
                <option value="suv">SUV</option>
                <option value="truck">Xe tải</option>
                <option value="bus">Xe khách</option>
              </select>
            </div>

            <!-- Hãng xe -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Hãng xe <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.vehicleBrand"
                type="text"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                placeholder="Nhập hãng xe"
                required
              />
            </div>

            <!-- Năm sản xuất -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Năm sản xuất <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.manufacturingYear"
                type="number"
                min="1990"
                max="2025"
                class="w-full h-[54px] px-[14px] border border-gray-300 rounded-[8px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                placeholder="Nhập năm sản xuất"
                required
              />
            </div>
          </div>

          <!-- Buttons -->
          <div class="flex justify-end gap-4 pt-8">
            <button
              type="button"
              @click="goBack"
              class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Quay lại
            </button>
            <button
              type="submit"
              class="px-6 py-3 bg-[#3079ff] text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Tiếp tục
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useHead({
  title: 'Bước 1: Khai báo thông tin mua bảo hiểm - BIC',
  meta: [
    {
      name: 'description',
      content: 'Khai báo thông tin chủ xe và thông tin xe để mua bảo hiểm bắt buộc trách nhiệm dân sự'
    }
  ]
})

// Router
const router = useRouter()

// Form data
const formData = ref({
  ownerName: '',
  phoneNumber: '',
  email: '',
  address: '',
  licensePlate: '',
  vehicleType: '',
  vehicleBrand: '',
  manufacturingYear: null
})

// Methods
const handleSubmit = () => {
  // Validate form data
  console.log('Form submitted:', formData.value)
  
  // Navigate to step 2
  router.push('/insurance/purchase/step-2')
}

const goBack = () => {
  router.back()
}
</script>

<style scoped>
/* Custom styles for form inputs */
input:focus, select:focus, textarea:focus {
  box-shadow: 0 0 0 2px rgba(48, 121, 255, 0.2);
}

/* Placeholder styling */
input::placeholder, textarea::placeholder {
  color: #919eab;
}

/* Select styling */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  appearance: none;
}
</style>
