<template>
  <div class="bg-[#ffffff] relative min-h-screen pb-[200px]">
    <!-- Title Section -->
    <div class="absolute box-border content-stretch flex flex-col gap-[9px] items-center justify-start p-0 top-[115px] translate-x-[-50%]" style="left: calc(50% + 0.5px)">
      <div class="box-border content-stretch flex flex-col gap-10 items-center justify-start p-0 relative shrink-0 w-full">
        <div class="flex flex-col font-['Helvetica_Neue:Bold',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[40px] text-center text-neutral-700 text-nowrap">
          <p class="block leading-[1.2] whitespace-pre">B<PERSON>o hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô </p>
        </div>
      </div>
      <div class="flex flex-col font-['Helvetica_Neue:Regular',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[16px] text-center text-neutral-700 w-full">
        <p class="block leading-[1.2]">
          Vui lòng cung cấp đầy đủ các thông tin dưới đây để tiếp tục.
        </p>
      </div>
    </div>

    <!-- Stepper Navigation -->
    <div class="absolute contents left-[576px] top-[219px]">
      <div class="absolute h-0 left-[674px] top-[251px] w-[257px]">
        <div class="absolute bottom-0 left-0 right-0 top-[-2px]">
          <img alt="" class="block max-w-none size-full" :src="imgLine1" />
        </div>
      </div>
      <div class="absolute flex h-0 items-center justify-center left-[991px] top-[251px] w-[253px]">
        <div class="flex-none rotate-[180deg]">
          <div class="h-0 relative w-[253px]">
            <div class="absolute bottom-0 left-0 right-0 top-[-2px]">
              <img alt="" class="block max-w-none size-full" :src="imgLine2" />
            </div>
          </div>
        </div>
      </div>

      <!-- Step 1 -->
      <div class="absolute box-border content-stretch flex flex-col gap-2 h-[115px] items-center justify-start left-[576px] p-0 top-[219px] w-[143px]">
        <div
          class="box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-4 py-3 relative rounded-[30px] shrink-0 size-[60px]"
          :class="currentStep >= 1 ? 'bg-[#3079ff]' : 'border border-[#3079ff] border-solid'"
        >
          <div
            class="flex flex-col font-['Helvetica_Neue:Bold',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[28px] text-center text-nowrap"
            :class="currentStep >= 1 ? 'text-[#ffffff]' : 'text-[#3079ff]'"
          >
            <p class="block leading-[1.2] whitespace-pre">1</p>
          </div>
        </div>
        <div
          class="basis-0 flex flex-col grow justify-center leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[16px] text-center w-full"
          :class="currentStep >= 1 ? 'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[#3079ff]' : 'font-[\'Helvetica_Neue:Regular\',_sans-serif] text-[#000000]'"
        >
          <p class="block leading-[1.2]">
            Khai báo thông tin mua bảo hiểm
          </p>
        </div>
      </div>

      <!-- Step 2 -->
      <div class="absolute box-border content-stretch flex flex-col gap-2 h-[106px] items-center justify-start left-[889px] p-0 top-[219px] w-[143px]">
        <div
          class="box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-4 py-3 relative rounded-[30px] shrink-0 size-[60px]"
          :class="currentStep >= 2 ? 'bg-[#3079ff]' : 'border border-[#3079ff] border-solid'"
        >
          <div class="absolute border border-[#3079ff] border-solid inset-0 pointer-events-none rounded-[30px]" v-if="currentStep < 2" />
          <div
            class="flex flex-col font-['Helvetica_Neue:Bold',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[28px] text-center text-nowrap"
            :class="currentStep >= 2 ? 'text-[#ffffff]' : 'text-[#3079ff]'"
          >
            <p class="block leading-[1.2] whitespace-pre">2</p>
          </div>
        </div>
        <div
          class="basis-0 flex flex-col grow justify-center leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[16px] text-center w-full"
          :class="currentStep >= 2 ? 'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[#3079ff]' : 'font-[\'Helvetica_Neue:Regular\',_sans-serif] text-[#000000]'"
        >
          <p class="block leading-[1.2]">Xác nhận thông tin</p>
        </div>
      </div>

      <!-- Step 3 -->
      <div class="absolute box-border content-stretch flex flex-col gap-2 h-[106px] items-center justify-start left-[1202px] p-0 top-[219px] w-[143px]">
        <div
          class="box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-4 py-3 relative rounded-[30px] shrink-0 size-[60px]"
          :class="currentStep >= 3 ? 'bg-[#3079ff]' : 'border border-[#3079ff] border-solid'"
        >
          <div class="absolute border border-[#3079ff] border-solid inset-0 pointer-events-none rounded-[30px]" v-if="currentStep < 3" />
          <div
            class="flex flex-col font-['Helvetica_Neue:Bold',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[28px] text-center text-nowrap"
            :class="currentStep >= 3 ? 'text-[#ffffff]' : 'text-[#3079ff]'"
          >
            <p class="block leading-[1.2] whitespace-pre">3</p>
          </div>
        </div>
        <div
          class="basis-0 flex flex-col grow justify-center leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[16px] text-center w-full"
          :class="currentStep >= 3 ? 'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[#3079ff]' : 'font-[\'Helvetica_Neue:Regular\',_sans-serif] text-[#000000]'"
        >
          <p class="block leading-[1.2]">Thanh toán</p>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="absolute bg-[#ffffff] left-[392px] rounded-lg top-[350px] w-[1140px] min-h-[1478px]">
      <div class="absolute border border-[#e9ecee] border-solid inset-[-0.5px] pointer-events-none rounded-[8.5px]" />

      <!-- Dynamic Content -->
      <div class="p-8">
        <NuxtPage />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useHead({
  title: 'Khai báo thông tin mua bảo hiểm - BIC',
  meta: [
    {
      name: 'description',
      content: 'Khai báo thông tin mua bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô'
    }
  ]
})

// Image assets
const imgLine1 = "http://localhost:3845/assets/771c9e029c63988d20c3b574c589fa3199fa86a7.svg"
const imgLine2 = "http://localhost:3845/assets/69b9f3373b571c23d809707ccd8077c51552bf3a.svg"

// Get current route to determine step
const route = useRoute()

// Determine current step based on route
const currentStep = computed(() => {
  const path = route.path
  if (path.includes('/step-2')) return 2
  if (path.includes('/step-3')) return 3
  return 1 // Default to step 1
})

// Redirect to step-1 if on index page
onMounted(() => {
  if (route.path === '/insurance/purchase' || route.path === '/insurance/purchase/') {
    navigateTo('/insurance/purchase/step-1')
  }
})
</script>

<style scoped>
/* Helvetica Neue font family */
.font-helvetica-neue-regular {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 400;
}

.font-helvetica-neue-bold {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 700;
}
</style>

<style scoped>
/* Custom styles for the insurance purchase page */
.font-helvetica-neue-regular {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 400;
}

.font-helvetica-neue-bold {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 700;
}
</style>
