<template>
  <!-- Step 2 Content -->
  <div class="flex flex-col items-center justify-center h-full min-h-[400px]">
          <div class="text-center space-y-6">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            
            <h2 class="text-3xl font-bold text-gray-800">Step 2 - Xác nhận thông tin</h2>
            
            <p class="text-lg text-gray-600 max-w-md">
              Trang này sẽ hiển thị thông tin xác nhận từ bước 1 và cho phép ng<PERSON>ời dùng kiểm tra lại trước khi thanh toán.
            </p>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-lg">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-yellow-800 font-medium">Đang phát triển</span>
              </div>
              <p class="text-yellow-700 mt-1 text-sm">
                Nội dung chi tiết của bước này sẽ được bổ sung sau.
              </p>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-center gap-4 pt-8">
              <button
                @click="goBack"
                class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Quay lại
              </button>
              <button
                @click="goToStep3"
                class="px-6 py-3 bg-[#3079ff] text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Tiếp tục thanh toán
              </button>
            </div>
          </div>
        </div>
</template>

<script setup lang="ts">
// SEO Meta
useHead({
  title: 'Bước 2: Xác nhận thông tin - BIC',
  meta: [
    {
      name: 'description',
      content: 'Xác nhận thông tin trước khi thanh toán bảo hiểm bắt buộc trách nhiệm dân sự'
    }
  ]
})

// Router
const router = useRouter()

// Methods
const goBack = () => {
  router.push('/insurance/purchase/step-1')
}

const goToStep3 = () => {
  router.push('/insurance/purchase/step-3')
}
</script>

<style scoped>
/* Custom styles for step 2 */
</style>
